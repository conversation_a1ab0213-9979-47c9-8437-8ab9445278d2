# 2-Input OR Gate Fault Injection Experiment

## Overview
This experiment demonstrates the basic principles of fault modeling, test set generation, fault detection, and simulation analysis in VLSI testing using a simple 2-input OR gate.

## Objectives Achieved
✅ **Circuit Under Test (CUT)** with fault injection capability  
✅ **Golden reference model** (fault-free)  
✅ **Stuck-at faults** (stuck-at-0 and stuck-at-1) on both inputs  
✅ **Parameterized fault control** for easy configuration  
✅ **Complete test set** generation (all 4 input combinations)  
✅ **Comparator module** for output comparison  
✅ **Fault detection reporting** (FAULT-FREE vs FAULTY)  
✅ **VCD waveform generation** for GTKWave analysis  

## Files Description

### Main Files
- `falut_injection.v` - Main fault injection testbench (fault-free configuration)
- `test_stuck_b_at_0.v` - Test scenario with B stuck-at-0 fault
- `test_stuck_a_at_1.v` - Test scenario with A stuck-at-1 fault

### Generated Files
- `*.vcd` - VCD waveform files for GTKWave analysis

## Circuit Architecture

### 1. Circuit Under Test (CUT)
```verilog
module cut_or (
    input  wire A, B,
    output wire Y
);
    parameter STUCK_A_AT_0 = 0;  // A stuck-at-0 fault
    parameter STUCK_A_AT_1 = 0;  // A stuck-at-1 fault  
    parameter STUCK_B_AT_0 = 0;  // B stuck-at-0 fault
    parameter STUCK_B_AT_1 = 0;  // B stuck-at-1 fault
    
    wire A_eff = (STUCK_A_AT_0) ? 1'b0 : (STUCK_A_AT_1) ? 1'b1 : A;
    wire B_eff = (STUCK_B_AT_0) ? 1'b0 : (STUCK_B_AT_1) ? 1'b1 : B;
    assign Y = A_eff | B_eff;
endmodule
```

### 2. Golden Reference Model
```verilog
module golden_or (
    input  wire A, B,
    output wire Yexp
);
    assign Yexp = A | B;  // Fault-free OR gate
endmodule
```

### 3. Test Pattern Generator
Generates all 4 test vectors: (0,0), (0,1), (1,0), (1,1)

### 4. Comparator
Compares CUT output with Golden model output and flags mismatches

## Test Set Analysis

### Complete Test Set for 2-Input OR Gate
| A | B | Expected Y | Test Purpose |
|---|---|------------|--------------|
| 0 | 0 | 0          | Detects A stuck-at-1, B stuck-at-1 |
| 0 | 1 | 1          | Detects B stuck-at-0 |
| 1 | 0 | 1          | Detects A stuck-at-0 |
| 1 | 1 | 1          | Redundant for OR gate |

### Fault Detection Capability
- **A stuck-at-0**: Detected by test vector (1,0)
- **A stuck-at-1**: Detected by test vector (0,0)  
- **B stuck-at-0**: Detected by test vector (0,1)
- **B stuck-at-1**: Detected by test vector (0,0)

## Usage Instructions

### Running Simulations

#### 1. Fault-Free Test
```bash
iverilog -o falut_injection falut_injection.v
./falut_injection
```

#### 2. B Stuck-at-0 Fault Test
```bash
iverilog -o test_stuck_b_at_0 test_stuck_b_at_0.v
./test_stuck_b_at_0
```

#### 3. A Stuck-at-1 Fault Test
```bash
iverilog -o test_stuck_a_at_1 test_stuck_a_at_1.v
./test_stuck_a_at_1
```

#### 4. Custom Fault Configuration
Edit the fault parameters in the testbench:
```verilog
localparam STUCK_A_AT_0 = 0;  // Set to 1 to inject fault
localparam STUCK_A_AT_1 = 0;  // Set to 1 to inject fault
localparam STUCK_B_AT_0 = 0;  // Set to 1 to inject fault  
localparam STUCK_B_AT_1 = 0;  // Set to 1 to inject fault
```

### Analyzing Waveforms with GTKWave

#### 1. Open VCD file
```bash
gtkwave falut_injection.vcd
```

#### 2. Key Signals to Observe
- **A, B**: Input test patterns
- **Y_cut**: Output from Circuit Under Test
- **Y_exp**: Expected output from Golden model
- **mismatch**: Fault detection flag
- **valid**: Test pattern valid signal

#### 3. Analysis Tips
- Compare Y_cut vs Y_exp to see fault effects
- Look for mismatch flag assertion when faults are detected
- Observe timing relationships between signals

## Expected Results

### Fault-Free Operation
```
=== 2-INPUT OR GATE FAULT INJECTION TEST ===
Fault Configuration: All faults = 0

Test Results:
   Time  A B | Y_cut Y_exp | Status    | Expected OR Result
  ------ ---|-------|-------|-----------|------------------
 45000  <USER> <GROUP> |   0     0  | MATCH     | 0 OR 0 = 0
 55000  0 1 |   1     1  | MATCH     | 0 OR 1 = 1
 65000  1 0 |   1     1  | MATCH     | 1 OR 0 = 1
 75000  1 1 |   1     1  | MATCH     | 1 OR 1 = 1

*** FINAL RESULT: FAULT-FREE - No faults detected ***
```

### B Stuck-at-0 Fault Detection
```
Test Results:
 45000  0 0 |   0     0  | MATCH     | 0 OR 0 = 0
 55000  0 1 |   0     1  | MISMATCH  | 0 OR 1 = 1 (fault detected!)
 65000  1 0 |   1     1  | MATCH     | 1 OR 0 = 1
 75000  1 1 |   1     1  | MATCH     | 1 OR 1 = 1

*** FINAL RESULT: FAULTY - Fault successfully detected! ***
```

## Educational Value
This experiment demonstrates:
1. **Fault Modeling**: How to model stuck-at faults in digital circuits
2. **Test Generation**: Systematic approach to generate test patterns
3. **Fault Coverage**: Understanding which faults can be detected
4. **Simulation-Based Testing**: Using simulation for fault detection
5. **Waveform Analysis**: Visual verification using GTKWave

## Extensions
- Add more fault types (bridging faults, delay faults)
- Implement larger circuits (AND gate, XOR gate, combinational circuits)
- Add automatic test pattern generation (ATPG)
- Implement fault simulation algorithms
