#! /usr/bin/vvp
:ivl_version "11.0 (stable)";
:ivl_delay_selection "TYPICAL";
:vpi_time_precision - 12;
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/system.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_sys.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_textio.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/v2005_math.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/va_math.vpi";
S_0x56c00847dcf0 .scope module, "tb" "tb" 2 66;
 .timescale -9 -12;
P_0x56c00847de80 .param/l "STUCK_B_AT_0" 1 2 76, +C4<00000000000000000000000000000000>;
v0x56c0084dfd30_0 .net "A", 0 0, v0x56c0084df530_0;  1 drivers
v0x56c0084dfdf0_0 .net "B", 0 0, v0x56c0084df640_0;  1 drivers
v0x56c0084dfeb0_0 .net "Y_cut", 0 0, L_0x56c0084e0640;  1 drivers
v0x56c0084dff80_0 .net "Y_exp", 0 0, L_0x56c0084e0740;  1 drivers
v0x56c0084e0070_0 .var "clk", 0 0;
v0x56c0084e01b0_0 .net "done", 0 0, v0x56c0084df7f0_0;  1 drivers
v0x56c0084e0250_0 .net "mismatch", 0 0, v0x56c0084de270_0;  1 drivers
v0x56c0084e02f0_0 .var "rst_n", 0 0;
v0x56c0084e03e0_0 .var "start", 0 0;
v0x56c0084e0510_0 .net "valid", 0 0, v0x56c0084dfba0_0;  1 drivers
E_0x56c0084beae0 .event posedge, v0x56c0084de1d0_0;
S_0x56c00847df20 .scope module, "COMP" "comparator" 2 81, 2 52 0, S_0x56c00847dcf0;
 .timescale -9 -12;
    .port_info 0 /INPUT 1 "clk";
    .port_info 1 /INPUT 1 "rst_n";
    .port_info 2 /INPUT 1 "valid";
    .port_info 3 /INPUT 1 "Y_cut";
    .port_info 4 /INPUT 1 "Y_exp";
    .port_info 5 /OUTPUT 1 "mismatch_seen";
v0x56c0084a7580_0 .net "Y_cut", 0 0, L_0x56c0084e0640;  alias, 1 drivers
v0x56c0084de110_0 .net "Y_exp", 0 0, L_0x56c0084e0740;  alias, 1 drivers
v0x56c0084de1d0_0 .net "clk", 0 0, v0x56c0084e0070_0;  1 drivers
v0x56c0084de270_0 .var "mismatch_seen", 0 0;
v0x56c0084de330_0 .net "rst_n", 0 0, v0x56c0084e02f0_0;  1 drivers
v0x56c0084de440_0 .net "valid", 0 0, v0x56c0084dfba0_0;  alias, 1 drivers
E_0x56c0084bf470/0 .event negedge, v0x56c0084de330_0;
E_0x56c0084bf470/1 .event posedge, v0x56c0084de1d0_0;
E_0x56c0084bf470 .event/or E_0x56c0084bf470/0, E_0x56c0084bf470/1;
S_0x56c0084de5c0 .scope module, "CUT" "cut_or" 2 79, 2 3 0, S_0x56c00847dcf0;
 .timescale -9 -12;
    .port_info 0 /INPUT 1 "A";
    .port_info 1 /INPUT 1 "B";
    .port_info 2 /OUTPUT 1 "Y";
P_0x56c0084de7c0 .param/l "STUCK_B_AT_0" 0 2 8, +C4<00000000000000000000000000000000>;
L_0x56c0084a7360 .functor BUFZ 1, v0x56c0084df640_0, C4<0>, C4<0>, C4<0>;
L_0x56c0084e0640 .functor OR 1, v0x56c0084df530_0, L_0x56c0084a7360, C4<0>, C4<0>;
v0x56c0084de8b0_0 .net "A", 0 0, v0x56c0084df530_0;  alias, 1 drivers
v0x56c0084de990_0 .net "B", 0 0, v0x56c0084df640_0;  alias, 1 drivers
v0x56c0084dea50_0 .net "B_eff", 0 0, L_0x56c0084a7360;  1 drivers
v0x56c0084deaf0_0 .net "Y", 0 0, L_0x56c0084e0640;  alias, 1 drivers
S_0x56c0084debf0 .scope module, "GOLD" "golden_or" 2 80, 2 13 0, S_0x56c00847dcf0;
 .timescale -9 -12;
    .port_info 0 /INPUT 1 "A";
    .port_info 1 /INPUT 1 "B";
    .port_info 2 /OUTPUT 1 "Yexp";
L_0x56c0084e0740 .functor OR 1, v0x56c0084df530_0, v0x56c0084df640_0, C4<0>, C4<0>;
v0x56c0084dee20_0 .net "A", 0 0, v0x56c0084df530_0;  alias, 1 drivers
v0x56c0084deef0_0 .net "B", 0 0, v0x56c0084df640_0;  alias, 1 drivers
v0x56c0084defc0_0 .net "Yexp", 0 0, L_0x56c0084e0740;  alias, 1 drivers
S_0x56c0084df0c0 .scope module, "PG" "pattern_gen_min" 2 78, 2 21 0, S_0x56c00847dcf0;
 .timescale -9 -12;
    .port_info 0 /INPUT 1 "clk";
    .port_info 1 /INPUT 1 "rst_n";
    .port_info 2 /INPUT 1 "start";
    .port_info 3 /OUTPUT 1 "A";
    .port_info 4 /OUTPUT 1 "B";
    .port_info 5 /OUTPUT 1 "valid";
    .port_info 6 /OUTPUT 1 "done";
P_0x56c0084aaef0 .param/l "IDLE" 1 2 31, C4<00>;
P_0x56c0084aaf30 .param/l "V0" 1 2 31, C4<01>;
P_0x56c0084aaf70 .param/l "V1" 1 2 31, C4<10>;
P_0x56c0084aafb0 .param/l "V2" 1 2 31, C4<11>;
v0x56c0084df530_0 .var "A", 0 0;
v0x56c0084df640_0 .var "B", 0 0;
v0x56c0084df750_0 .net "clk", 0 0, v0x56c0084e0070_0;  alias, 1 drivers
v0x56c0084df7f0_0 .var "done", 0 0;
v0x56c0084df890_0 .var "ns", 1 0;
v0x56c0084df980_0 .net "rst_n", 0 0, v0x56c0084e02f0_0;  alias, 1 drivers
v0x56c0084dfa20_0 .var "s", 1 0;
v0x56c0084dfae0_0 .net "start", 0 0, v0x56c0084e03e0_0;  1 drivers
v0x56c0084dfba0_0 .var "valid", 0 0;
E_0x56c0084aea80 .event edge, v0x56c0084dfa20_0, v0x56c0084dfae0_0;
    .scope S_0x56c0084df0c0;
T_0 ;
    %wait E_0x56c0084bf470;
    %load/vec4 v0x56c0084df980_0;
    %nor/r;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.0, 8;
    %pushi/vec4 0, 0, 2;
    %assign/vec4 v0x56c0084dfa20_0, 0;
    %jmp T_0.1;
T_0.0 ;
    %load/vec4 v0x56c0084df890_0;
    %assign/vec4 v0x56c0084dfa20_0, 0;
T_0.1 ;
    %jmp T_0;
    .thread T_0;
    .scope S_0x56c0084df0c0;
T_1 ;
    %wait E_0x56c0084aea80;
    %load/vec4 v0x56c0084dfa20_0;
    %store/vec4 v0x56c0084df890_0, 0, 2;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x56c0084dfba0_0, 0, 1;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x56c0084df7f0_0, 0, 1;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x56c0084df530_0, 0, 1;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x56c0084df640_0, 0, 1;
    %load/vec4 v0x56c0084dfa20_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_1.0, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_1.1, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_1.2, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_1.3, 6;
    %jmp T_1.4;
T_1.0 ;
    %load/vec4 v0x56c0084dfae0_0;
    %flag_set/vec4 8;
    %jmp/0xz  T_1.5, 8;
    %pushi/vec4 1, 0, 2;
    %store/vec4 v0x56c0084df890_0, 0, 2;
T_1.5 ;
    %jmp T_1.4;
T_1.1 ;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x56c0084df530_0, 0, 1;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x56c0084df640_0, 0, 1;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x56c0084dfba0_0, 0, 1;
    %pushi/vec4 2, 0, 2;
    %store/vec4 v0x56c0084df890_0, 0, 2;
    %jmp T_1.4;
T_1.2 ;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x56c0084df530_0, 0, 1;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x56c0084df640_0, 0, 1;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x56c0084dfba0_0, 0, 1;
    %pushi/vec4 3, 0, 2;
    %store/vec4 v0x56c0084df890_0, 0, 2;
    %jmp T_1.4;
T_1.3 ;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x56c0084df530_0, 0, 1;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x56c0084df640_0, 0, 1;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x56c0084dfba0_0, 0, 1;
    %pushi/vec4 0, 0, 2;
    %store/vec4 v0x56c0084df890_0, 0, 2;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x56c0084df7f0_0, 0, 1;
    %jmp T_1.4;
T_1.4 ;
    %pop/vec4 1;
    %jmp T_1;
    .thread T_1, $push;
    .scope S_0x56c00847df20;
T_2 ;
    %wait E_0x56c0084bf470;
    %load/vec4 v0x56c0084de330_0;
    %nor/r;
    %flag_set/vec4 8;
    %jmp/0xz  T_2.0, 8;
    %pushi/vec4 0, 0, 1;
    %assign/vec4 v0x56c0084de270_0, 0;
    %jmp T_2.1;
T_2.0 ;
    %load/vec4 v0x56c0084de440_0;
    %load/vec4 v0x56c0084a7580_0;
    %load/vec4 v0x56c0084de110_0;
    %cmp/ne;
    %flag_get/vec4 6;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_2.2, 8;
    %pushi/vec4 1, 0, 1;
    %assign/vec4 v0x56c0084de270_0, 0;
T_2.2 ;
T_2.1 ;
    %jmp T_2;
    .thread T_2;
    .scope S_0x56c00847dcf0;
T_3 ;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x56c0084e0070_0, 0, 1;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x56c0084e02f0_0, 0, 1;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x56c0084e03e0_0, 0, 1;
    %end;
    .thread T_3;
    .scope S_0x56c00847dcf0;
T_4 ;
    %delay 5000, 0;
    %load/vec4 v0x56c0084e0070_0;
    %inv;
    %store/vec4 v0x56c0084e0070_0, 0, 1;
    %jmp T_4;
    .thread T_4;
    .scope S_0x56c00847dcf0;
T_5 ;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x56c0084e02f0_0, 0, 1;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x56c0084e03e0_0, 0, 1;
    %delay 20000, 0;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x56c0084e02f0_0, 0, 1;
    %delay 10000, 0;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x56c0084e03e0_0, 0, 1;
    %delay 10000, 0;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x56c0084e03e0_0, 0, 1;
    %vpi_call 2 90 "$display", "   time  A B | Y_cut Y_exp | status" {0 0 0};
T_5.0 ;
    %wait E_0x56c0084beae0;
    %load/vec4 v0x56c0084e0510_0;
    %flag_set/vec4 8;
    %jmp/0xz  T_5.1, 8;
    %load/vec4 v0x56c0084dfeb0_0;
    %load/vec4 v0x56c0084dff80_0;
    %cmp/e;
    %jmp/0xz  T_5.3, 6;
    %vpi_call 2 96 "$display", "%6t  %0d %0d |   %0d     %0d  | match", $time, v0x56c0084dfd30_0, v0x56c0084dfdf0_0, v0x56c0084dfeb0_0, v0x56c0084dff80_0 {0 0 0};
    %jmp T_5.4;
T_5.3 ;
    %vpi_call 2 98 "$display", "%6t  %0d %0d |   %0d     %0d  | MISMATCH", $time, v0x56c0084dfd30_0, v0x56c0084dfdf0_0, v0x56c0084dfeb0_0, v0x56c0084dff80_0 {0 0 0};
T_5.4 ;
T_5.1 ;
    %load/vec4 v0x56c0084e01b0_0;
    %flag_set/vec4 8;
    %jmp/0xz  T_5.5, 8;
    %load/vec4 v0x56c0084e0250_0;
    %flag_set/vec4 8;
    %jmp/0xz  T_5.7, 8;
    %vpi_call 2 101 "$display", "\012Result: FAULTY" {0 0 0};
    %jmp T_5.8;
T_5.7 ;
    %vpi_call 2 102 "$display", "\012Result: FAULT-FREE" {0 0 0};
T_5.8 ;
    %delay 10000, 0;
    %vpi_call 2 103 "$finish" {0 0 0};
T_5.5 ;
    %jmp T_5.0;
    %end;
    .thread T_5;
    .scope S_0x56c00847dcf0;
T_6 ;
    %vpi_call 2 110 "$dumpfile", "falut_injection.vcd" {0 0 0};
    %vpi_call 2 111 "$dumpvars", 32'sb00000000000000000000000000000000, S_0x56c00847dcf0 {0 0 0};
    %end;
    .thread T_6;
# The file index is used to find the file name in the following table.
:file_names 3;
    "N/A";
    "<interactive>";
    "falut_injection.v";
