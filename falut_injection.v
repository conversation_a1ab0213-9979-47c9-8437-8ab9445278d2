`timescale 1ns/1ps

// Enhanced Circuit Under Test (CUT) with comprehensive fault injection
module cut_or (
    input  wire A,
    input  wire B,
    output wire Y
);
    // Fault injection parameters
    parameter STUCK_A_AT_0 = 0;  // Stuck-at-0 fault on input A
    parameter STUCK_A_AT_1 = 0;  // Stuck-at-1 fault on input A
    parameter STUCK_B_AT_0 = 0;  // Stuck-at-0 fault on input B
    parameter STUCK_B_AT_1 = 0;  // Stuck-at-1 fault on input B

    // Apply fault injection to inputs
    wire A_eff = (STUCK_A_AT_0) ? 1'b0 :
                 (STUCK_A_AT_1) ? 1'b1 : A;
    wire B_eff = (STUCK_B_AT_0) ? 1'b0 :
                 (STUCK_B_AT_1) ? 1'b1 : B;

    // OR gate implementation with potentially faulty inputs
    assign Y = A_eff | B_eff;
endmodule

module golden_or (
    input  wire A,
    input  wire B,
    output wire Yexp
);
    assign Yexp = A | B;
endmodule

// Enhanced pattern generator with complete test coverage
module pattern_gen_complete (
    input  wire clk,
    input  wire rst_n,
    input  wire start,
    output reg  A,
    output reg  B,
    output reg  valid,
    output reg  done
);
    reg [2:0] s, ns;
    localparam IDLE=3'b000, V0=3'b001, V1=3'b010, V2=3'b011, V3=3'b100;

    always @ (posedge clk or negedge rst_n) begin
        if (!rst_n) s <= IDLE;
        else s <= ns;
    end

    always @* begin
        ns = s;
        valid = 1'b0;
        done  = 1'b0;
        A = 1'b0; B = 1'b0;
        case (s)
            IDLE: if (start) ns = V0;
            V0: begin A=1'b0; B=1'b0; valid=1'b1; ns=V1; end  // Test vector 00
            V1: begin A=1'b0; B=1'b1; valid=1'b1; ns=V2; end  // Test vector 01
            V2: begin A=1'b1; B=1'b0; valid=1'b1; ns=V3; end  // Test vector 10
            V3: begin A=1'b1; B=1'b1; valid=1'b1; ns=IDLE; done=1'b1; end  // Test vector 11
        endcase
    end
endmodule

module comparator (
    input  wire clk,
    input  wire rst_n,
    input  wire valid,
    input  wire Y_cut,
    input  wire Y_exp,
    output reg  mismatch_seen
);
    always @ (posedge clk or negedge rst_n) begin
        if (!rst_n) mismatch_seen <= 1'b0;
        else if (valid && (Y_cut !== Y_exp)) mismatch_seen <= 1'b1;
    end
endmodule

// Enhanced testbench with multiple fault scenarios
module tb;
    reg clk = 0;
    always #5 clk = ~clk;
    reg rst_n = 0;
    reg start = 0;

    wire A, B, valid, done;
    wire Y_cut, Y_exp;
    wire mismatch;

    // Fault configuration parameters - Change these to test different faults
    localparam STUCK_A_AT_0 = 0;  // Set to 1 to inject A stuck-at-0 fault
    localparam STUCK_A_AT_1 = 0;  // Set to 1 to inject A stuck-at-1 fault
    localparam STUCK_B_AT_0 = 0;  // Set to 1 to inject B stuck-at-0 fault
    localparam STUCK_B_AT_1 = 0;  // Set to 1 to inject B stuck-at-1 fault

    // Module instantiations
    pattern_gen_complete PG(.clk(clk), .rst_n(rst_n), .start(start), .A(A), .B(B), .valid(valid), .done(done));
    cut_or #(.STUCK_A_AT_0(STUCK_A_AT_0), .STUCK_A_AT_1(STUCK_A_AT_1),
             .STUCK_B_AT_0(STUCK_B_AT_0), .STUCK_B_AT_1(STUCK_B_AT_1))
           CUT(.A(A), .B(B), .Y(Y_cut));
    golden_or GOLD(.A(A), .B(B), .Yexp(Y_exp));
    comparator COMP(.clk(clk), .rst_n(rst_n), .valid(valid), .Y_cut(Y_cut), .Y_exp(Y_exp), .mismatch_seen(mismatch));

    initial begin
        // Display fault configuration
        $display("=== 2-INPUT OR GATE FAULT INJECTION TEST ===");
        $display("Fault Configuration:");
        $display("  A stuck-at-0: %0d", STUCK_A_AT_0);
        $display("  A stuck-at-1: %0d", STUCK_A_AT_1);
        $display("  B stuck-at-0: %0d", STUCK_B_AT_0);
        $display("  B stuck-at-1: %0d", STUCK_B_AT_1);
        $display("");

        // Reset & start sequence
        rst_n = 0; start = 0;
        #20 rst_n = 1;
        #10 start  = 1;
        #10 start  = 0;

        $display("Test Results:");
        $display("   Time  A B | Y_cut Y_exp | Status    | Expected OR Result");
        $display("  ------ ---|-------|-------|-----------|------------------");

        forever begin
            @(posedge clk);
            if (valid) begin
                if (Y_cut === Y_exp)
                    $display("%6t  %0d %0d |   %0d     %0d  | MATCH     | %0d OR %0d = %0d",
                            $time, A, B, Y_cut, Y_exp, A, B, Y_exp);
                else
                    $display("%6t  %0d %0d |   %0d     %0d  | MISMATCH  | %0d OR %0d = %0d (fault detected!)",
                            $time, A, B, Y_cut, Y_exp, A, B, Y_exp);
            end
            if (done) begin
                $display("  ------ ---|-------|-------|-----------|------------------");
                if (mismatch)
                    $display("\n*** FINAL RESULT: FAULTY - Fault successfully detected! ***");
                else
                    $display("\n*** FINAL RESULT: FAULT-FREE - No faults detected ***");
                $display("\nVCD file generated: falut_injection.vcd");
                $display("Use GTKWave to analyze waveforms: gtkwave falut_injection.vcd");
                #10 $finish;
            end
        end
    end

    //  Waveform dump block (always generates falut_injection.vcd)
    initial begin
        $dumpfile("falut_injection.vcd");
        $dumpvars(0, tb);
    end
endmodule
