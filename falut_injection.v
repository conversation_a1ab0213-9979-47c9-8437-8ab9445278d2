`timescale 1ns/1ps

module cut_or (
    input  wire A,
    input  wire B,
    output wire Y
);
    parameter STUCK_B_AT_0 = 0;
    wire B_eff = (STUCK_B_AT_0) ? 1'b0 : B;
    assign Y = A | B_eff;
endmodule

module golden_or (
    input  wire A,
    input  wire B,
    output wire Yexp
);
    assign Yexp = A | B;
endmodule

module pattern_gen_min (
    input  wire clk,
    input  wire rst_n,
    input  wire start,
    output reg  A,
    output reg  B,
    output reg  valid,
    output reg  done
);
    reg [1:0] s, ns;
    localparam IDLE=2'b00, V0=2'b01, V1=2'b10, V2=2'b11;

    always @ (posedge clk or negedge rst_n) begin
        if (!rst_n) s <= IDLE;
        else s <= ns;
    end

    always @* begin
        ns = s;
        valid = 1'b0;
        done  = 1'b0;
        A = 1'b0; B = 1'b0;
        case (s)
            IDLE: if (start) ns = V0;
            V0: begin A=1'b0; B=1'b0; valid=1'b1; ns=V1; end
            V1: begin A=1'b0; B=1'b1; valid=1'b1; ns=V2; end
            V2: begin A=1'b1; B=1'b0; valid=1'b1; ns=IDLE; done=1'b1; end
        endcase
    end
endmodule

module comparator (
    input  wire clk,
    input  wire rst_n,
    input  wire valid,
    input  wire Y_cut,
    input  wire Y_exp,
    output reg  mismatch_seen
);
    always @ (posedge clk or negedge rst_n) begin
        if (!rst_n) mismatch_seen <= 1'b0;
        else if (valid && (Y_cut !== Y_exp)) mismatch_seen <= 1'b1;
    end
endmodule

module tb;
    reg clk = 0;
    always #5 clk = ~clk;
    reg rst_n = 0;
    reg start = 0;

    wire A, B, valid, done;
    wire Y_cut, Y_exp;
    wire mismatch;

    localparam STUCK_B_AT_0 = 0;

    pattern_gen_min PG(.clk(clk), .rst_n(rst_n), .start(start), .A(A), .B(B), .valid(valid), .done(done));
    cut_or #(.STUCK_B_AT_0(STUCK_B_AT_0)) CUT(.A(A), .B(B), .Y(Y_cut));
    golden_or GOLD(.A(A), .B(B), .Yexp(Y_exp));
    comparator COMP(.clk(clk), .rst_n(rst_n), .valid(valid), .Y_cut(Y_cut), .Y_exp(Y_exp), .mismatch_seen(mismatch));

    initial begin
        // reset & start sequence
        rst_n = 0; start = 0;
        #20 rst_n = 1;
        #10 start  = 1;
        #10 start  = 0;

        $display("   time  A B | Y_cut Y_exp | status");

        forever begin
            @(posedge clk);
            if (valid) begin
                if (Y_cut === Y_exp)
                    $display("%6t  %0d %0d |   %0d     %0d  | match", $time, A, B, Y_cut, Y_exp);
                else
                    $display("%6t  %0d %0d |   %0d     %0d  | MISMATCH", $time, A, B, Y_cut, Y_exp);
            end
            if (done) begin
                if (mismatch) $display("\nResult: FAULTY");
                else $display("\nResult: FAULT-FREE");
                #10 $finish;
            end
        end
    end

    //  Waveform dump block (always generates falut_injection.vcd)
    initial begin
        $dumpfile("falut_injection.vcd");
        $dumpvars(0, tb);
    end
endmodule
