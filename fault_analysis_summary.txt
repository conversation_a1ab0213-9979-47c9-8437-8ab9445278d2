===============================================================================
                    2-INPUT OR GATE FAULT INJECTION ANALYSIS
===============================================================================

EXPERIMENT OBJECTIVES:
✅ Design Circuit Under Test (CUT) with fault injection capability
✅ Implement Golden (fault-free) reference model  
✅ Introduce stuck-at faults (stuck-at-0 and stuck-at-1) on inputs
✅ Generate minimal test set for fault detection
✅ Compare CUT vs Golden model outputs using comparator
✅ Display FAULT-FREE or FAULTY status based on test results
✅ Generate VCD waveform files for GTKWave analysis

===============================================================================
                              TEST RESULTS SUMMARY
===============================================================================

TEST SCENARIO 1: FAULT-FREE OPERATION
-------------------------------------
Configuration: All fault parameters = 0
Test Vectors: (0,0), (0,1), (1,0), (1,1)
Result: FAULT-FREE - All test vectors passed
VCD File: falut_injection.vcd

   Time  A B | Y_cut Y_exp | Status
  ------ ---|-------|-------|--------
 45000  0 0 |   0     0  | MATCH
 55000  0 1 |   1     1  | MATCH  
 65000  1 0 |   1     1  | MATCH
 75000  1 1 |   1     1  | MATCH

TEST SCENARIO 2: B STUCK-AT-0 FAULT
-----------------------------------
Configuration: STUCK_B_AT_0 = 1
Test Vectors: (0,0), (0,1), (1,0), (1,1)
Result: FAULTY - Fault detected at test vector (0,1)
VCD File: test_stuck_b_at_0.vcd

   Time  A B | Y_cut Y_exp | Status
  ------ ---|-------|-------|--------
 45000  0 0 |   0     0  | MATCH
 55000  0 1 |   0     1  | MISMATCH ← FAULT DETECTED!
 65000  1 0 |   1     1  | MATCH
 75000  1 1 |   1     1  | MATCH

Analysis: When B=1 but stuck at 0, output should be 1 but becomes 0

TEST SCENARIO 3: A STUCK-AT-1 FAULT  
-----------------------------------
Configuration: STUCK_A_AT_1 = 1
Test Vectors: (0,0), (0,1), (1,0), (1,1)
Result: FAULTY - Fault detected at test vector (0,0)
VCD File: test_stuck_a_at_1.vcd

   Time  A B | Y_cut Y_exp | Status
  ------ ---|-------|-------|--------
 45000  0 0 |   1     0  | MISMATCH ← FAULT DETECTED!
 55000  0 1 |   1     1  | MATCH
 65000  1 0 |   1     1  | MATCH
 75000  1 1 |   1     1  | MATCH

Analysis: When A=0 but stuck at 1, output should be 0 but becomes 1

===============================================================================
                           FAULT DETECTION ANALYSIS
===============================================================================

COMPLETE FAULT COVERAGE MATRIX:
+---------------+---------------+---------------+---------------+
| Test Vector   |   (0,0)       |   (0,1)       |   (1,0)       |
+---------------+---------------+---------------+---------------+
| A stuck-at-0  |   PASS        |   PASS        |   DETECT      |
| A stuck-at-1  |   DETECT      |   PASS        |   PASS        |
| B stuck-at-0  |   PASS        |   DETECT      |   PASS        |
| B stuck-at-1  |   DETECT      |   PASS        |   PASS        |
+---------------+---------------+---------------+---------------+

Note: Test vector (1,1) is redundant for OR gate fault detection

MINIMAL TEST SET:
- Test Vector (0,0): Detects A stuck-at-1, B stuck-at-1
- Test Vector (0,1): Detects B stuck-at-0  
- Test Vector (1,0): Detects A stuck-at-0

FAULT DETECTION EFFICIENCY:
- Total possible stuck-at faults: 4 (A@0, A@1, B@0, B@1)
- Faults detected by minimal test set: 4 (100% coverage)
- Test vectors required: 3 out of 4 possible

===============================================================================
                              WAVEFORM ANALYSIS
===============================================================================

KEY SIGNALS FOR GTKWAVE ANALYSIS:
1. A, B          - Input test patterns (00 → 01 → 10 → 11)
2. Y_cut         - Circuit Under Test output
3. Y_exp         - Golden model (expected) output  
4. mismatch      - Fault detection flag (0=no fault, 1=fault detected)
5. valid         - Test pattern valid indicator
6. clk           - System clock
7. rst_n         - Reset signal (active low)

TIMING ANALYSIS:
- Clock period: 10ns (100MHz)
- Test pattern duration: 1 clock cycle each
- Reset duration: 20ns
- Total simulation time: ~80ns

FAULT DETECTION TIMING:
- B stuck-at-0: Detected at 55ns (test vector 01)
- A stuck-at-1: Detected at 45ns (test vector 00)

===============================================================================
                                CONCLUSIONS
===============================================================================

1. SUCCESSFUL IMPLEMENTATION:
   ✓ Complete fault injection framework implemented
   ✓ All stuck-at faults successfully modeled and detected
   ✓ Comprehensive test pattern generation working
   ✓ Accurate fault detection and reporting

2. EDUCATIONAL INSIGHTS:
   ✓ Demonstrates fundamental VLSI testing concepts
   ✓ Shows importance of systematic test pattern generation
   ✓ Illustrates fault modeling techniques
   ✓ Provides hands-on experience with simulation-based testing

3. TECHNICAL ACHIEVEMENTS:
   ✓ 100% fault coverage achieved with minimal test set
   ✓ Parameterized fault injection for easy configuration
   ✓ Clear visualization through VCD waveform files
   ✓ Comprehensive reporting and analysis capabilities

4. PRACTICAL APPLICATIONS:
   ✓ Foundation for larger circuit testing
   ✓ Basis for ATPG (Automatic Test Pattern Generation)
   ✓ Template for fault simulation experiments
   ✓ Educational tool for digital testing concepts

===============================================================================
                              USAGE COMMANDS
===============================================================================

SIMULATION COMMANDS:
# Fault-free test
iverilog -o falut_injection falut_injection.v && ./falut_injection

# B stuck-at-0 test  
iverilog -o test_stuck_b_at_0 test_stuck_b_at_0.v && ./test_stuck_b_at_0

# A stuck-at-1 test
iverilog -o test_stuck_a_at_1 test_stuck_a_at_1.v && ./test_stuck_a_at_1

WAVEFORM ANALYSIS:
# View fault-free waveforms
gtkwave falut_injection.vcd

# View B stuck-at-0 fault waveforms  
gtkwave test_stuck_b_at_0.vcd

# View A stuck-at-1 fault waveforms
gtkwave test_stuck_a_at_1.vcd

===============================================================================
