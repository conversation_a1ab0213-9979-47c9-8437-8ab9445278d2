$date
	Wed Aug 20 18:24:13 2025
$end
$version
	Icarus Verilog
$end
$timescale
	1ps
$end
$scope module tb $end
$var wire 1 ! valid $end
$var wire 1 " mismatch $end
$var wire 1 # done $end
$var wire 1 $ Y_exp $end
$var wire 1 % Y_cut $end
$var wire 1 & B $end
$var wire 1 ' A $end
$var reg 1 ( clk $end
$var reg 1 ) rst_n $end
$var reg 1 * start $end
$scope module COMP $end
$var wire 1 ( clk $end
$var wire 1 ) rst_n $end
$var wire 1 ! valid $end
$var wire 1 $ Y_exp $end
$var wire 1 % Y_cut $end
$var reg 1 " mismatch_seen $end
$upscope $end
$scope module CUT $end
$var wire 1 + A_eff $end
$var wire 1 , B_eff $end
$var wire 1 % Y $end
$var wire 1 & B $end
$var wire 1 ' A $end
$upscope $end
$scope module GOLD $end
$var wire 1 $ Yexp $end
$var wire 1 & B $end
$var wire 1 ' A $end
$upscope $end
$scope module PG $end
$var wire 1 ( clk $end
$var wire 1 ) rst_n $end
$var wire 1 * start $end
$var reg 1 ' A $end
$var reg 1 & B $end
$var reg 1 # done $end
$var reg 3 - ns [2:0] $end
$var reg 3 . s [2:0] $end
$var reg 1 ! valid $end
$upscope $end
$upscope $end
$enddefinitions $end
#0
$dumpvars
b0 .
b0 -
0,
1+
0*
0)
0(
0'
0&
1%
0$
0#
0"
0!
$end
#5000
1(
#10000
0(
#15000
1(
#20000
0(
1)
#25000
1(
#30000
b1 -
0(
1*
#35000
b10 -
1!
b1 .
1(
#40000
1!
b10 -
0(
0*
#45000
1$
b11 -
1,
1&
1!
1"
b10 .
1(
#50000
0(
#55000
b100 -
1'
0,
0&
1!
b11 .
1(
#60000
0(
#65000
1#
b0 -
1,
1&
1'
1!
b100 .
1(
#70000
0(
#75000
0$
0,
0&
0'
0#
0!
b0 .
1(
#80000
0(
#85000
1(
