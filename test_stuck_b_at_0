#! /usr/bin/vvp
:ivl_version "11.0 (stable)";
:ivl_delay_selection "TYPICAL";
:vpi_time_precision - 12;
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/system.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_sys.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_textio.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/v2005_math.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/va_math.vpi";
S_0x5973f0a88cf0 .scope module, "tb" "tb" 2 83;
 .timescale -9 -12;
P_0x5973f0ab73c0 .param/l "STUCK_A_AT_0" 1 2 94, +C4<00000000000000000000000000000000>;
P_0x5973f0ab7400 .param/l "STUCK_A_AT_1" 1 2 95, +C4<00000000000000000000000000000000>;
P_0x5973f0ab7440 .param/l "STUCK_B_AT_0" 1 2 96, +C4<00000000000000000000000000000001>;
P_0x5973f0ab7480 .param/l "STUCK_B_AT_1" 1 2 97, +C4<00000000000000000000000000000000>;
v0x5973f0af5a90_0 .net "A", 0 0, v0x5973f0af5290_0;  1 drivers
v0x5973f0af5b50_0 .net "B", 0 0, v0x5973f0af53a0_0;  1 drivers
v0x5973f0af5c10_0 .net "Y_cut", 0 0, L_0x5973f0abc8a0;  1 drivers
v0x5973f0af5ce0_0 .net "Y_exp", 0 0, L_0x5973f0abd770;  1 drivers
v0x5973f0af5dd0_0 .var "clk", 0 0;
v0x5973f0af5f10_0 .net "done", 0 0, v0x5973f0af5550_0;  1 drivers
v0x5973f0af5fb0_0 .net "mismatch", 0 0, v0x5973f0abc910_0;  1 drivers
v0x5973f0af6050_0 .var "rst_n", 0 0;
v0x5973f0af6140_0 .var "start", 0 0;
v0x5973f0af6270_0 .net "valid", 0 0, v0x5973f0af5900_0;  1 drivers
E_0x5973f0acb3f0 .event posedge, v0x5973f0ab3130_0;
S_0x5973f0a88fc0 .scope module, "COMP" "comparator" 2 105, 2 68 0, S_0x5973f0a88cf0;
 .timescale -9 -12;
    .port_info 0 /INPUT 1 "clk";
    .port_info 1 /INPUT 1 "rst_n";
    .port_info 2 /INPUT 1 "valid";
    .port_info 3 /INPUT 1 "Y_cut";
    .port_info 4 /INPUT 1 "Y_exp";
    .port_info 5 /OUTPUT 1 "mismatch_seen";
v0x5973f0ac1aa0_0 .net "Y_cut", 0 0, L_0x5973f0abc8a0;  alias, 1 drivers
v0x5973f0ab3030_0 .net "Y_exp", 0 0, L_0x5973f0abd770;  alias, 1 drivers
v0x5973f0ab3130_0 .net "clk", 0 0, v0x5973f0af5dd0_0;  1 drivers
v0x5973f0abc910_0 .var "mismatch_seen", 0 0;
v0x5973f0abc9b0_0 .net "rst_n", 0 0, v0x5973f0af6050_0;  1 drivers
v0x5973f0af3df0_0 .net "valid", 0 0, v0x5973f0af5900_0;  alias, 1 drivers
E_0x5973f0acbda0/0 .event negedge, v0x5973f0abc9b0_0;
E_0x5973f0acbda0/1 .event posedge, v0x5973f0ab3130_0;
E_0x5973f0acbda0 .event/or E_0x5973f0acbda0/0, E_0x5973f0acbda0/1;
S_0x5973f0af3f70 .scope module, "CUT" "cut_or" 2 103, 2 6 0, S_0x5973f0a88cf0;
 .timescale -9 -12;
    .port_info 0 /INPUT 1 "A";
    .port_info 1 /INPUT 1 "B";
    .port_info 2 /OUTPUT 1 "Y";
P_0x5973f0ad4fb0 .param/l "STUCK_A_AT_0" 0 2 12, +C4<00000000000000000000000000000000>;
P_0x5973f0ad4ff0 .param/l "STUCK_A_AT_1" 0 2 13, +C4<00000000000000000000000000000000>;
P_0x5973f0ad5030 .param/l "STUCK_B_AT_0" 0 2 14, +C4<00000000000000000000000000000001>;
P_0x5973f0ad5070 .param/l "STUCK_B_AT_1" 0 2 15, +C4<00000000000000000000000000000000>;
L_0x5973f0ab2f10 .functor BUFZ 1, v0x5973f0af5290_0, C4<0>, C4<0>, C4<0>;
L_0x7a0b13186018 .functor BUFT 1, C4<0>, C4<0>, C4<0>, C4<0>;
L_0x5973f0abc8a0 .functor OR 1, L_0x5973f0ab2f10, L_0x7a0b13186018, C4<0>, C4<0>;
v0x5973f0af4390_0 .net "A", 0 0, v0x5973f0af5290_0;  alias, 1 drivers
v0x5973f0af4470_0 .net "A_eff", 0 0, L_0x5973f0ab2f10;  1 drivers
v0x5973f0af4530_0 .net "B", 0 0, v0x5973f0af53a0_0;  alias, 1 drivers
v0x5973f0af4600_0 .net "B_eff", 0 0, L_0x7a0b13186018;  1 drivers
v0x5973f0af46c0_0 .net "Y", 0 0, L_0x5973f0abc8a0;  alias, 1 drivers
S_0x5973f0af4810 .scope module, "GOLD" "golden_or" 2 104, 2 27 0, S_0x5973f0a88cf0;
 .timescale -9 -12;
    .port_info 0 /INPUT 1 "A";
    .port_info 1 /INPUT 1 "B";
    .port_info 2 /OUTPUT 1 "Yexp";
L_0x5973f0abd770 .functor OR 1, v0x5973f0af5290_0, v0x5973f0af53a0_0, C4<0>, C4<0>;
v0x5973f0af4a70_0 .net "A", 0 0, v0x5973f0af5290_0;  alias, 1 drivers
v0x5973f0af4b40_0 .net "B", 0 0, v0x5973f0af53a0_0;  alias, 1 drivers
v0x5973f0af4c10_0 .net "Yexp", 0 0, L_0x5973f0abd770;  alias, 1 drivers
S_0x5973f0af4d10 .scope module, "PG" "pattern_gen_complete" 2 100, 2 36 0, S_0x5973f0a88cf0;
 .timescale -9 -12;
    .port_info 0 /INPUT 1 "clk";
    .port_info 1 /INPUT 1 "rst_n";
    .port_info 2 /INPUT 1 "start";
    .port_info 3 /OUTPUT 1 "A";
    .port_info 4 /OUTPUT 1 "B";
    .port_info 5 /OUTPUT 1 "valid";
    .port_info 6 /OUTPUT 1 "done";
P_0x5973f0af4ef0 .param/l "IDLE" 1 2 46, C4<000>;
P_0x5973f0af4f30 .param/l "V0" 1 2 46, C4<001>;
P_0x5973f0af4f70 .param/l "V1" 1 2 46, C4<010>;
P_0x5973f0af4fb0 .param/l "V2" 1 2 46, C4<011>;
P_0x5973f0af4ff0 .param/l "V3" 1 2 46, C4<100>;
v0x5973f0af5290_0 .var "A", 0 0;
v0x5973f0af53a0_0 .var "B", 0 0;
v0x5973f0af54b0_0 .net "clk", 0 0, v0x5973f0af5dd0_0;  alias, 1 drivers
v0x5973f0af5550_0 .var "done", 0 0;
v0x5973f0af55f0_0 .var "ns", 2 0;
v0x5973f0af56e0_0 .net "rst_n", 0 0, v0x5973f0af6050_0;  alias, 1 drivers
v0x5973f0af5780_0 .var "s", 2 0;
v0x5973f0af5840_0 .net "start", 0 0, v0x5973f0af6140_0;  1 drivers
v0x5973f0af5900_0 .var "valid", 0 0;
E_0x5973f0ac0770 .event edge, v0x5973f0af5780_0, v0x5973f0af5840_0;
    .scope S_0x5973f0af4d10;
T_0 ;
    %wait E_0x5973f0acbda0;
    %load/vec4 v0x5973f0af56e0_0;
    %nor/r;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.0, 8;
    %pushi/vec4 0, 0, 3;
    %assign/vec4 v0x5973f0af5780_0, 0;
    %jmp T_0.1;
T_0.0 ;
    %load/vec4 v0x5973f0af55f0_0;
    %assign/vec4 v0x5973f0af5780_0, 0;
T_0.1 ;
    %jmp T_0;
    .thread T_0;
    .scope S_0x5973f0af4d10;
T_1 ;
    %wait E_0x5973f0ac0770;
    %load/vec4 v0x5973f0af5780_0;
    %store/vec4 v0x5973f0af55f0_0, 0, 3;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x5973f0af5900_0, 0, 1;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x5973f0af5550_0, 0, 1;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x5973f0af5290_0, 0, 1;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x5973f0af53a0_0, 0, 1;
    %load/vec4 v0x5973f0af5780_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 3;
    %cmp/u;
    %jmp/1 T_1.0, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 3;
    %cmp/u;
    %jmp/1 T_1.1, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 3;
    %cmp/u;
    %jmp/1 T_1.2, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 3;
    %cmp/u;
    %jmp/1 T_1.3, 6;
    %dup/vec4;
    %pushi/vec4 4, 0, 3;
    %cmp/u;
    %jmp/1 T_1.4, 6;
    %jmp T_1.5;
T_1.0 ;
    %load/vec4 v0x5973f0af5840_0;
    %flag_set/vec4 8;
    %jmp/0xz  T_1.6, 8;
    %pushi/vec4 1, 0, 3;
    %store/vec4 v0x5973f0af55f0_0, 0, 3;
T_1.6 ;
    %jmp T_1.5;
T_1.1 ;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x5973f0af5290_0, 0, 1;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x5973f0af53a0_0, 0, 1;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x5973f0af5900_0, 0, 1;
    %pushi/vec4 2, 0, 3;
    %store/vec4 v0x5973f0af55f0_0, 0, 3;
    %jmp T_1.5;
T_1.2 ;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x5973f0af5290_0, 0, 1;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x5973f0af53a0_0, 0, 1;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x5973f0af5900_0, 0, 1;
    %pushi/vec4 3, 0, 3;
    %store/vec4 v0x5973f0af55f0_0, 0, 3;
    %jmp T_1.5;
T_1.3 ;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x5973f0af5290_0, 0, 1;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x5973f0af53a0_0, 0, 1;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x5973f0af5900_0, 0, 1;
    %pushi/vec4 4, 0, 3;
    %store/vec4 v0x5973f0af55f0_0, 0, 3;
    %jmp T_1.5;
T_1.4 ;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x5973f0af5290_0, 0, 1;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x5973f0af53a0_0, 0, 1;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x5973f0af5900_0, 0, 1;
    %pushi/vec4 0, 0, 3;
    %store/vec4 v0x5973f0af55f0_0, 0, 3;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x5973f0af5550_0, 0, 1;
    %jmp T_1.5;
T_1.5 ;
    %pop/vec4 1;
    %jmp T_1;
    .thread T_1, $push;
    .scope S_0x5973f0a88fc0;
T_2 ;
    %wait E_0x5973f0acbda0;
    %load/vec4 v0x5973f0abc9b0_0;
    %nor/r;
    %flag_set/vec4 8;
    %jmp/0xz  T_2.0, 8;
    %pushi/vec4 0, 0, 1;
    %assign/vec4 v0x5973f0abc910_0, 0;
    %jmp T_2.1;
T_2.0 ;
    %load/vec4 v0x5973f0af3df0_0;
    %load/vec4 v0x5973f0ac1aa0_0;
    %load/vec4 v0x5973f0ab3030_0;
    %cmp/ne;
    %flag_get/vec4 6;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_2.2, 8;
    %pushi/vec4 1, 0, 1;
    %assign/vec4 v0x5973f0abc910_0, 0;
T_2.2 ;
T_2.1 ;
    %jmp T_2;
    .thread T_2;
    .scope S_0x5973f0a88cf0;
T_3 ;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x5973f0af5dd0_0, 0, 1;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x5973f0af6050_0, 0, 1;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x5973f0af6140_0, 0, 1;
    %end;
    .thread T_3;
    .scope S_0x5973f0a88cf0;
T_4 ;
    %delay 5000, 0;
    %load/vec4 v0x5973f0af5dd0_0;
    %inv;
    %store/vec4 v0x5973f0af5dd0_0, 0, 1;
    %jmp T_4;
    .thread T_4;
    .scope S_0x5973f0a88cf0;
T_5 ;
    %vpi_call 2 109 "$display", "=== 2-INPUT OR GATE FAULT INJECTION TEST ===" {0 0 0};
    %vpi_call 2 110 "$display", "Test Scenario: B STUCK-AT-0 FAULT" {0 0 0};
    %vpi_call 2 111 "$display", "Fault Configuration:" {0 0 0};
    %vpi_call 2 112 "$display", "  A stuck-at-0: %0d", P_0x5973f0ab73c0 {0 0 0};
    %vpi_call 2 113 "$display", "  A stuck-at-1: %0d", P_0x5973f0ab7400 {0 0 0};
    %vpi_call 2 114 "$display", "  B stuck-at-0: %0d", P_0x5973f0ab7440 {0 0 0};
    %vpi_call 2 115 "$display", "  B stuck-at-1: %0d", P_0x5973f0ab7480 {0 0 0};
    %vpi_call 2 116 "$display", "\000" {0 0 0};
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x5973f0af6050_0, 0, 1;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x5973f0af6140_0, 0, 1;
    %delay 20000, 0;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x5973f0af6050_0, 0, 1;
    %delay 10000, 0;
    %pushi/vec4 1, 0, 1;
    %store/vec4 v0x5973f0af6140_0, 0, 1;
    %delay 10000, 0;
    %pushi/vec4 0, 0, 1;
    %store/vec4 v0x5973f0af6140_0, 0, 1;
    %vpi_call 2 124 "$display", "Test Results:" {0 0 0};
    %vpi_call 2 125 "$display", "   Time  A B | Y_cut Y_exp | Status    | Expected OR Result" {0 0 0};
    %vpi_call 2 126 "$display", "  ------ ---|-------|-------|-----------|------------------" {0 0 0};
T_5.0 ;
    %wait E_0x5973f0acb3f0;
    %load/vec4 v0x5973f0af6270_0;
    %flag_set/vec4 8;
    %jmp/0xz  T_5.1, 8;
    %load/vec4 v0x5973f0af5c10_0;
    %load/vec4 v0x5973f0af5ce0_0;
    %cmp/e;
    %jmp/0xz  T_5.3, 6;
    %vpi_call 2 132 "$display", "%6t  %0d %0d |   %0d     %0d  | MATCH     | %0d OR %0d = %0d", $time, v0x5973f0af5a90_0, v0x5973f0af5b50_0, v0x5973f0af5c10_0, v0x5973f0af5ce0_0, v0x5973f0af5a90_0, v0x5973f0af5b50_0, v0x5973f0af5ce0_0 {0 0 0};
    %jmp T_5.4;
T_5.3 ;
    %vpi_call 2 135 "$display", "%6t  %0d %0d |   %0d     %0d  | MISMATCH  | %0d OR %0d = %0d (fault detected!)", $time, v0x5973f0af5a90_0, v0x5973f0af5b50_0, v0x5973f0af5c10_0, v0x5973f0af5ce0_0, v0x5973f0af5a90_0, v0x5973f0af5b50_0, v0x5973f0af5ce0_0 {0 0 0};
T_5.4 ;
T_5.1 ;
    %load/vec4 v0x5973f0af5f10_0;
    %flag_set/vec4 8;
    %jmp/0xz  T_5.5, 8;
    %vpi_call 2 139 "$display", "  ------ ---|-------|-------|-----------|------------------" {0 0 0};
    %load/vec4 v0x5973f0af5fb0_0;
    %flag_set/vec4 8;
    %jmp/0xz  T_5.7, 8;
    %vpi_call 2 141 "$display", "\012*** FINAL RESULT: FAULTY - Fault successfully detected! ***" {0 0 0};
    %jmp T_5.8;
T_5.7 ;
    %vpi_call 2 143 "$display", "\012*** FINAL RESULT: FAULT-FREE - No faults detected ***" {0 0 0};
T_5.8 ;
    %vpi_call 2 144 "$display", "\012VCD file generated: test_stuck_b_at_0.vcd" {0 0 0};
    %vpi_call 2 145 "$display", "Use GTKWave to analyze waveforms: gtkwave test_stuck_b_at_0.vcd" {0 0 0};
    %delay 10000, 0;
    %vpi_call 2 146 "$finish" {0 0 0};
T_5.5 ;
    %jmp T_5.0;
    %end;
    .thread T_5;
    .scope S_0x5973f0a88cf0;
T_6 ;
    %vpi_call 2 153 "$dumpfile", "test_stuck_b_at_0.vcd" {0 0 0};
    %vpi_call 2 154 "$dumpvars", 32'sb00000000000000000000000000000000, S_0x5973f0a88cf0 {0 0 0};
    %end;
    .thread T_6;
# The file index is used to find the file name in the following table.
:file_names 3;
    "N/A";
    "<interactive>";
    "test_stuck_b_at_0.v";
