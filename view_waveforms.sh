#!/bin/bash

# GTKWave Waveform Viewer Script for Fault Injection Experiment

echo "=== GTKWave Waveform Viewer for Fault Injection Experiment ==="
echo ""
echo "Available VCD files:"
ls -1 *.vcd 2>/dev/null || echo "No VCD files found!"
echo ""

echo "Choose a test scenario:"
echo "1) Fault-free operation (falut_injection.vcd)"
echo "2) B stuck-at-0 fault (test_stuck_b_at_0.vcd)"  
echo "3) A stuck-at-1 fault (test_stuck_a_at_1.vcd)"
echo "4) All files (open multiple GTKWave windows)"
echo ""

read -p "Enter your choice (1-4): " choice

case $choice in
    1)
        echo "Opening fault-free waveforms..."
        gtkwave falut_injection.vcd &
        ;;
    2)
        echo "Opening B stuck-at-0 fault waveforms..."
        gtkwave test_stuck_b_at_0.vcd &
        ;;
    3)
        echo "Opening A stuck-at-1 fault waveforms..."
        gtkwave test_stuck_a_at_1.vcd &
        ;;
    4)
        echo "Opening all waveform files..."
        gtkwave falut_injection.vcd &
        sleep 1
        gtkwave test_stuck_b_at_0.vcd &
        sleep 1
        gtkwave test_stuck_a_at_1.vcd &
        ;;
    *)
        echo "Invalid choice!"
        exit 1
        ;;
esac

echo ""
echo "=== GTKWave Usage Instructions ==="
echo ""
echo "When GTKWave opens:"
echo "1. Look for the Signal Search Tree (SST) panel on the left"
echo "2. Expand 'tb' to see all signals"
echo "3. Select and drag these signals to the waveform viewer:"
echo "   - clk (clock)"
echo "   - rst_n (reset)"  
echo "   - start (start signal)"
echo "   - valid (test pattern valid)"
echo "   - A (input A)"
echo "   - B (input B)"
echo "   - Y_cut (CUT output)"
echo "   - Y_exp (expected output)"
echo "   - mismatch (fault detection)"
echo "   - done (test complete)"
echo ""
echo "4. Use Ctrl+Alt+F to zoom fit"
echo "5. Use mouse wheel to zoom in/out"
echo "6. Click on waveforms to place time cursor"
echo ""
echo "=== Signal Analysis Tips ==="
echo ""
echo "• Compare Y_cut vs Y_exp to see fault effects"
echo "• Watch for mismatch flag assertion when faults detected"
echo "• Observe test pattern sequence: 00 → 01 → 10 → 11"
echo "• Check timing relationships between signals"
echo ""
